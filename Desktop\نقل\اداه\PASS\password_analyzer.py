#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محلل قوة كلمات المرور
Password Strength Analyzer
"""

import re
import math
import string
from colorama import Fore, Style
from datetime import timedelta

class PasswordAnalyzer:
    def __init__(self):
        self.common_passwords = [
            "123456", "password", "123456789", "12345678", "12345",
            "1234567", "1234567890", "qwerty", "abc123", "111111",
            "123123", "admin", "letmein", "welcome", "monkey",
            "password123", "123qwe", "qwerty123", "iloveyou", "princess"
        ]
        
        self.common_patterns = [
            r"123+",  # أرقام متتالية
            r"abc+",  # حروف متتالية
            r"(.)\1{2,}",  # تكرار نفس الحرف
            r"qwerty",  # نمط لوحة المفاتيح
            r"asdf",  # نمط لوحة المفاتيح
        ]
    
    def analyze_password(self, password):
        """تحليل شامل لكلمة المرور"""
        analysis = {
            'password': password,
            'length': len(password),
            'character_sets': self._analyze_character_sets(password),
            'entropy': self._calculate_entropy(password),
            'strength_score': 0,
            'strength_level': '',
            'time_to_crack': '',
            'common_password': password.lower() in [p.lower() for p in self.common_passwords],
            'common_patterns': self._check_common_patterns(password),
            'recommendations': []
        }
        
        # حساب نقاط القوة
        analysis['strength_score'] = self._calculate_strength_score(analysis)
        analysis['strength_level'] = self._get_strength_level(analysis['strength_score'])
        analysis['time_to_crack'] = self._estimate_crack_time(analysis['entropy'])
        analysis['recommendations'] = self._generate_recommendations(analysis)
        
        return analysis
    
    def _analyze_character_sets(self, password):
        """تحليل أنواع الأحرف المستخدمة"""
        sets = {
            'lowercase': bool(re.search(r'[a-z]', password)),
            'uppercase': bool(re.search(r'[A-Z]', password)),
            'digits': bool(re.search(r'[0-9]', password)),
            'special': bool(re.search(r'[^a-zA-Z0-9]', password)),
            'unicode': bool(re.search(r'[^\x00-\x7F]', password))
        }
        sets['count'] = sum(sets.values())
        return sets
    
    def _calculate_entropy(self, password):
        """حساب الإنتروبيا (العشوائية) لكلمة المرور"""
        if not password:
            return 0
        
        # حساب حجم مجموعة الأحرف المستخدمة
        charset_size = 0
        if re.search(r'[a-z]', password):
            charset_size += 26
        if re.search(r'[A-Z]', password):
            charset_size += 26
        if re.search(r'[0-9]', password):
            charset_size += 10
        if re.search(r'[^a-zA-Z0-9]', password):
            charset_size += 32  # تقدير للرموز الخاصة
        
        if charset_size == 0:
            return 0
        
        # الإنتروبيا = طول كلمة المرور × log2(حجم مجموعة الأحرف)
        entropy = len(password) * math.log2(charset_size)
        return entropy
    
    def _calculate_strength_score(self, analysis):
        """حساب نقاط قوة كلمة المرور"""
        score = 0
        
        # نقاط الطول
        length = analysis['length']
        if length >= 12:
            score += 25
        elif length >= 8:
            score += 15
        elif length >= 6:
            score += 10
        else:
            score += 0
        
        # نقاط تنوع الأحرف
        char_sets = analysis['character_sets']
        score += char_sets['count'] * 5
        
        # نقاط الإنتروبيا
        entropy = analysis['entropy']
        if entropy >= 60:
            score += 25
        elif entropy >= 40:
            score += 15
        elif entropy >= 25:
            score += 10
        else:
            score += 0
        
        # خصم نقاط للكلمات الشائعة
        if analysis['common_password']:
            score -= 30
        
        # خصم نقاط للأنماط الشائعة
        score -= len(analysis['common_patterns']) * 10
        
        return max(0, min(100, score))
    
    def _get_strength_level(self, score):
        """تحديد مستوى قوة كلمة المرور"""
        if score >= 80:
            return "قوية جداً"
        elif score >= 60:
            return "قوية"
        elif score >= 40:
            return "متوسطة"
        elif score >= 20:
            return "ضعيفة"
        else:
            return "ضعيفة جداً"
    
    def _estimate_crack_time(self, entropy):
        """تقدير الوقت المطلوب لكسر كلمة المرور"""
        if entropy <= 0:
            return "فوري"
        
        # افتراض 1 مليار محاولة في الثانية
        attempts_per_second = 1e9
        total_combinations = 2 ** entropy
        
        # متوسط الوقت = نصف إجمالي التركيبات
        average_time_seconds = (total_combinations / 2) / attempts_per_second
        
        if average_time_seconds < 1:
            return "أقل من ثانية"
        elif average_time_seconds < 60:
            return f"{average_time_seconds:.1f} ثانية"
        elif average_time_seconds < 3600:
            return f"{average_time_seconds/60:.1f} دقيقة"
        elif average_time_seconds < 86400:
            return f"{average_time_seconds/3600:.1f} ساعة"
        elif average_time_seconds < 31536000:
            return f"{average_time_seconds/86400:.1f} يوم"
        else:
            years = average_time_seconds / 31536000
            if years < 1000:
                return f"{years:.1f} سنة"
            elif years < 1000000:
                return f"{years/1000:.1f} ألف سنة"
            else:
                return f"{years/1000000:.1f} مليون سنة"
    
    def _check_common_patterns(self, password):
        """فحص الأنماط الشائعة في كلمة المرور"""
        found_patterns = []
        for pattern in self.common_patterns:
            if re.search(pattern, password.lower()):
                found_patterns.append(pattern)
        return found_patterns
    
    def _generate_recommendations(self, analysis):
        """توليد توصيات لتحسين كلمة المرور"""
        recommendations = []
        
        if analysis['length'] < 8:
            recommendations.append("استخدم كلمة مرور أطول (8 أحرف على الأقل)")
        elif analysis['length'] < 12:
            recommendations.append("استخدم كلمة مرور أطول (12 حرف أو أكثر للأمان الأفضل)")
        
        char_sets = analysis['character_sets']
        if not char_sets['lowercase']:
            recommendations.append("أضف أحرف صغيرة (a-z)")
        if not char_sets['uppercase']:
            recommendations.append("أضف أحرف كبيرة (A-Z)")
        if not char_sets['digits']:
            recommendations.append("أضف أرقام (0-9)")
        if not char_sets['special']:
            recommendations.append("أضف رموز خاصة (!@#$%^&*)")
        
        if analysis['common_password']:
            recommendations.append("تجنب استخدام كلمات المرور الشائعة")
        
        if analysis['common_patterns']:
            recommendations.append("تجنب الأنماط الشائعة مثل 123 أو abc")
        
        if analysis['entropy'] < 40:
            recommendations.append("زد من عشوائية كلمة المرور")
        
        return recommendations
    
    def display_analysis(self, analysis):
        """عرض نتائج التحليل"""
        print(f"\n{Fore.CYAN}{'='*60}")
        print(f"تحليل كلمة المرور")
        print(f"{'='*60}{Style.RESET_ALL}")
        
        # معلومات أساسية
        print(f"{Fore.YELLOW}الطول:{Style.RESET_ALL} {analysis['length']} حرف")
        print(f"{Fore.YELLOW}الإنتروبيا:{Style.RESET_ALL} {analysis['entropy']:.1f} بت")
        
        # نقاط القوة
        score = analysis['strength_score']
        level = analysis['strength_level']
        color = Fore.GREEN if score >= 60 else Fore.YELLOW if score >= 40 else Fore.RED
        print(f"{Fore.YELLOW}نقاط القوة:{Style.RESET_ALL} {color}{score}/100{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}مستوى القوة:{Style.RESET_ALL} {color}{level}{Style.RESET_ALL}")
        
        # وقت الكسر
        print(f"{Fore.YELLOW}الوقت المقدر للكسر:{Style.RESET_ALL} {analysis['time_to_crack']}")
        
        # أنواع الأحرف
        char_sets = analysis['character_sets']
        print(f"\n{Fore.CYAN}أنواع الأحرف المستخدمة:{Style.RESET_ALL}")
        print(f"  أحرف صغيرة: {'✓' if char_sets['lowercase'] else '✗'}")
        print(f"  أحرف كبيرة: {'✓' if char_sets['uppercase'] else '✗'}")
        print(f"  أرقام: {'✓' if char_sets['digits'] else '✗'}")
        print(f"  رموز خاصة: {'✓' if char_sets['special'] else '✗'}")
        
        # تحذيرات
        if analysis['common_password']:
            print(f"\n{Fore.RED}⚠️  تحذير: كلمة مرور شائعة!{Style.RESET_ALL}")
        
        if analysis['common_patterns']:
            print(f"\n{Fore.RED}⚠️  تحذير: تحتوي على أنماط شائعة!{Style.RESET_ALL}")
        
        # التوصيات
        if analysis['recommendations']:
            print(f"\n{Fore.CYAN}التوصيات للتحسين:{Style.RESET_ALL}")
            for i, rec in enumerate(analysis['recommendations'], 1):
                print(f"  {i}. {rec}")
        else:
            print(f"\n{Fore.GREEN}✓ كلمة مرور قوية! لا توجد توصيات للتحسين.{Style.RESET_ALL}")

if __name__ == "__main__":
    analyzer = PasswordAnalyzer()
    
    # اختبار بعض كلمات المرور
    test_passwords = [
        "123456",
        "password",
        "MyP@ssw0rd123",
        "Tr0ub4dor&3",
        "correct horse battery staple"
    ]
    
    for pwd in test_passwords:
        print(f"\n{Fore.MAGENTA}اختبار كلمة المرور: {pwd}{Style.RESET_ALL}")
        result = analyzer.analyze_password(pwd)
        analyzer.display_analysis(result)
