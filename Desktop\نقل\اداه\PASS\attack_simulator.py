#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محاكي الهجمات التعليمية
Educational Attack Simulator
"""

import time
import random
import string
import itertools
from colorama import Fore, Style
from tqdm import tqdm
import threading

class AttackSimulator:
    def __init__(self):
        self.common_passwords = [
            "123456", "password", "123456789", "12345678", "12345",
            "1234567", "1234567890", "qwerty", "abc123", "111111",
            "123123", "admin", "letmein", "welcome", "monkey",
            "password123", "123qwe", "qwerty123", "iloveyou", "princess",
            "dragon", "654321", "batman", "superman", "hello",
            "freedom", "whatever", "master", "trustno1", "jordan23"
        ]
        
        self.leaked_credentials = [
            ("admin", "admin"),
            ("user", "password"),
            ("test", "test123"),
            ("demo", "demo"),
            ("guest", "guest"),
            ("root", "toor"),
            ("administrator", "password"),
            ("user1", "123456"),
            ("admin", "123456"),
            ("test", "password")
        ]
        
        self.rate_limit_attempts = 0
        self.rate_limit_window = 60  # ثانية
        self.max_attempts = 5
        
    def brute_force_simulation(self, target_password, max_length=4):
        """محاكاة هجوم Brute Force"""
        print(f"\n{Fore.CYAN}بدء محاكاة هجوم Brute Force{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}الهدف: كسر كلمة المرور '{target_password}'{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}الحد الأقصى للطول: {max_length} أحرف{Style.RESET_ALL}")
        
        charset = string.ascii_lowercase + string.digits
        attempts = 0
        start_time = time.time()
        
        # محاولة جميع التركيبات الممكنة
        for length in range(1, max_length + 1):
            print(f"\n{Fore.CYAN}جاري اختبار كلمات المرور بطول {length} أحرف...{Style.RESET_ALL}")
            
            total_combinations = len(charset) ** length
            with tqdm(total=total_combinations, desc="التقدم", unit="محاولة") as pbar:
                
                for combination in itertools.product(charset, repeat=length):
                    attempts += 1
                    candidate = ''.join(combination)
                    
                    # محاكاة تأخير الشبكة
                    time.sleep(0.001)
                    
                    if candidate == target_password:
                        end_time = time.time()
                        duration = end_time - start_time
                        
                        print(f"\n{Fore.GREEN}✓ تم العثور على كلمة المرور!{Style.RESET_ALL}")
                        print(f"{Fore.GREEN}كلمة المرور: {candidate}{Style.RESET_ALL}")
                        print(f"{Fore.YELLOW}عدد المحاولات: {attempts:,}{Style.RESET_ALL}")
                        print(f"{Fore.YELLOW}الوقت المستغرق: {duration:.2f} ثانية{Style.RESET_ALL}")
                        print(f"{Fore.YELLOW}معدل المحاولات: {attempts/duration:.0f} محاولة/ثانية{Style.RESET_ALL}")
                        return True
                    
                    pbar.update(1)
                    
                    # إيقاف المحاكاة إذا استغرقت وقتاً طويلاً
                    if attempts > 10000:
                        print(f"\n{Fore.RED}تم إيقاف المحاكاة - عدد كبير جداً من المحاولات{Style.RESET_ALL}")
                        return False
        
        print(f"\n{Fore.RED}✗ لم يتم العثور على كلمة المرور{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}إجمالي المحاولات: {attempts:,}{Style.RESET_ALL}")
        return False
    
    def dictionary_attack_simulation(self, target_password):
        """محاكاة هجوم Dictionary Attack"""
        print(f"\n{Fore.CYAN}بدء محاكاة هجوم Dictionary Attack{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}الهدف: كسر كلمة المرور '{target_password}'{Style.RESET_ALL}")
        
        attempts = 0
        start_time = time.time()
        
        # إنشاء قاموس موسع
        dictionary = self.common_passwords.copy()
        
        # إضافة تنويعات شائعة
        variations = []
        for pwd in self.common_passwords[:10]:  # أول 10 كلمات فقط لتوفير الوقت
            variations.extend([
                pwd + "123",
                pwd + "1",
                pwd + "!",
                pwd.capitalize(),
                pwd.upper(),
                "123" + pwd,
                pwd + "2023",
                pwd + "2024"
            ])
        
        dictionary.extend(variations)
        
        print(f"{Fore.YELLOW}حجم القاموس: {len(dictionary)} كلمة{Style.RESET_ALL}")
        
        with tqdm(total=len(dictionary), desc="التقدم", unit="كلمة") as pbar:
            for candidate in dictionary:
                attempts += 1
                
                # محاكاة تأخير الشبكة
                time.sleep(0.01)
                
                if candidate == target_password:
                    end_time = time.time()
                    duration = end_time - start_time
                    
                    print(f"\n{Fore.GREEN}✓ تم العثور على كلمة المرور في القاموس!{Style.RESET_ALL}")
                    print(f"{Fore.GREEN}كلمة المرور: {candidate}{Style.RESET_ALL}")
                    print(f"{Fore.YELLOW}عدد المحاولات: {attempts}{Style.RESET_ALL}")
                    print(f"{Fore.YELLOW}الوقت المستغرق: {duration:.2f} ثانية{Style.RESET_ALL}")
                    return True
                
                pbar.update(1)
        
        print(f"\n{Fore.RED}✗ لم يتم العثور على كلمة المرور في القاموس{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}إجمالي المحاولات: {attempts}{Style.RESET_ALL}")
        return False
    
    def credential_stuffing_simulation(self, target_password):
        """محاكاة هجوم Credential Stuffing"""
        print(f"\n{Fore.CYAN}بدء محاكاة هجوم Credential Stuffing{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}الهدف: اختبار بيانات اعتماد مسربة{Style.RESET_ALL}")
        
        attempts = 0
        start_time = time.time()
        
        print(f"{Fore.YELLOW}عدد بيانات الاعتماد المسربة: {len(self.leaked_credentials)}{Style.RESET_ALL}")
        
        with tqdm(total=len(self.leaked_credentials), desc="التقدم", unit="محاولة") as pbar:
            for username, password in self.leaked_credentials:
                attempts += 1
                
                # محاكاة تأخير الشبكة
                time.sleep(0.1)
                
                print(f"  جاري اختبار: {username}:{password}")
                
                if password == target_password:
                    end_time = time.time()
                    duration = end_time - start_time
                    
                    print(f"\n{Fore.GREEN}✓ تم العثور على كلمة المرور في البيانات المسربة!{Style.RESET_ALL}")
                    print(f"{Fore.GREEN}المستخدم: {username}{Style.RESET_ALL}")
                    print(f"{Fore.GREEN}كلمة المرور: {password}{Style.RESET_ALL}")
                    print(f"{Fore.YELLOW}عدد المحاولات: {attempts}{Style.RESET_ALL}")
                    print(f"{Fore.YELLOW}الوقت المستغرق: {duration:.2f} ثانية{Style.RESET_ALL}")
                    return True
                
                pbar.update(1)
        
        print(f"\n{Fore.RED}✗ لم يتم العثور على كلمة المرور في البيانات المسربة{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}إجمالي المحاولات: {attempts}{Style.RESET_ALL}")
        return False
    
    def rate_limiting_test(self):
        """اختبار حماية Rate Limiting"""
        print(f"\n{Fore.CYAN}اختبار حماية Rate Limiting{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}الحد الأقصى: {self.max_attempts} محاولات كل {self.rate_limit_window} ثانية{Style.RESET_ALL}")
        
        self.rate_limit_attempts = 0
        start_time = time.time()
        
        for attempt in range(10):  # محاولة 10 مرات
            current_time = time.time()
            
            # إعادة تعيين العداد إذا انتهت النافزة الزمنية
            if current_time - start_time >= self.rate_limit_window:
                self.rate_limit_attempts = 0
                start_time = current_time
                print(f"{Fore.GREEN}تم إعادة تعيين عداد المحاولات{Style.RESET_ALL}")
            
            if self.rate_limit_attempts >= self.max_attempts:
                remaining_time = self.rate_limit_window - (current_time - start_time)
                print(f"{Fore.RED}✗ تم حظر المحاولة {attempt + 1} - تجاوز الحد الأقصى{Style.RESET_ALL}")
                print(f"{Fore.YELLOW}الوقت المتبقي للحظر: {remaining_time:.1f} ثانية{Style.RESET_ALL}")
            else:
                self.rate_limit_attempts += 1
                print(f"{Fore.GREEN}✓ المحاولة {attempt + 1} مسموحة ({self.rate_limit_attempts}/{self.max_attempts}){Style.RESET_ALL}")
            
            time.sleep(2)  # انتظار ثانيتين بين المحاولات
        
        print(f"\n{Fore.CYAN}انتهاء اختبار Rate Limiting{Style.RESET_ALL}")
    
    def simulate_advanced_attacks(self):
        """محاكاة الهجمات المتقدمة"""
        print(f"\n{Fore.CYAN}محاكاة الهجمات المتقدمة{Style.RESET_ALL}")
        
        attacks = [
            "Malicious Browser Extension",
            "Stealer Malware (RedLine)",
            "API Abuse",
            "Session Hijacking",
            "MITM Attack"
        ]
        
        for attack in attacks:
            print(f"\n{Fore.YELLOW}محاكاة: {attack}{Style.RESET_ALL}")
            
            # محاكاة عملية الهجوم
            for step in range(1, 4):
                time.sleep(1)
                if attack == "Malicious Browser Extension":
                    steps = ["تثبيت الإضافة الخبيثة", "جمع بيانات المتصفح", "إرسال البيانات للمهاجم"]
                elif attack == "Stealer Malware (RedLine)":
                    steps = ["إصابة النظام", "سرقة كلمات المرور المحفوظة", "إرسال البيانات"]
                elif attack == "API Abuse":
                    steps = ["اكتشاف نقطة ضعف في API", "استغلال النقطة", "الوصول للبيانات"]
                else:
                    steps = [f"الخطوة {step}", f"الخطوة {step+1}", f"الخطوة {step+2}"]
                
                if step <= len(steps):
                    print(f"  {step}. {steps[step-1]}")
            
            print(f"{Fore.RED}  ⚠️  تحذير: هذا النوع من الهجمات خطير جداً!{Style.RESET_ALL}")

if __name__ == "__main__":
    simulator = AttackSimulator()
    
    # اختبار محاكي الهجمات
    test_password = "abc123"
    
    print(f"{Fore.MAGENTA}اختبار محاكي الهجمات مع كلمة المرور: {test_password}{Style.RESET_ALL}")
    
    # اختبار Dictionary Attack
    simulator.dictionary_attack_simulation(test_password)
    
    # اختبار Rate Limiting
    simulator.rate_limiting_test()
