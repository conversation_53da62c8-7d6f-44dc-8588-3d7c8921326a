#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات الأمان والحماية
Security and Protection Tools
"""

import hashlib
import bcrypt
import secrets
import string
import time
import json
from datetime import datetime, timedelta
from colorama import Fore, Style
from cryptography.fernet import Fernet
import argon2

class SecurityTools:
    def __init__(self):
        self.breached_passwords = {
            # قاعدة بيانات مبسطة لكلمات المرور المسربة
            "123456": "HaveIBeenPwned",
            "password": "HaveIBeenPwned", 
            "123456789": "HaveIBeenPwned",
            "12345678": "HaveIBeenPwned",
            "12345": "HaveIBeenPwned",
            "qwerty": "HaveIBeenPwned",
            "abc123": "HaveIBeenPwned",
            "admin": "Multiple Breaches",
            "letmein": "HaveIBeenPwned",
            "welcome": "HaveIBeenPwned"
        }
        
        self.login_attempts = {}
        self.blocked_ips = {}
        self.max_attempts = 5
        self.block_duration = 300  # 5 دقائق
        
    def hash_password(self, password, method="bcrypt"):
        """تشفير كلمة المرور بطرق مختلفة"""
        if method == "bcrypt":
            # BCrypt - الأكثر أماناً
            salt = bcrypt.gensalt()
            hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
            return hashed.decode('utf-8')
        
        elif method == "argon2":
            # Argon2 - الأحدث والأكثر أماناً
            ph = argon2.PasswordHasher()
            return ph.hash(password)
        
        elif method == "pbkdf2":
            # PBKDF2 - جيد ومدعوم على نطاق واسع
            salt = secrets.token_bytes(32)
            key = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt, 100000)
            return salt.hex() + ':' + key.hex()
        
        elif method == "sha256":
            # SHA256 - غير آمن للاستخدام المباشر
            return hashlib.sha256(password.encode('utf-8')).hexdigest()
        
        else:
            raise ValueError("طريقة تشفير غير مدعومة")
    
    def verify_password(self, password, hashed_password, method="bcrypt"):
        """التحقق من كلمة المرور المشفرة"""
        try:
            if method == "bcrypt":
                return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
            
            elif method == "argon2":
                ph = argon2.PasswordHasher()
                ph.verify(hashed_password, password)
                return True
            
            elif method == "pbkdf2":
                salt_hex, key_hex = hashed_password.split(':')
                salt = bytes.fromhex(salt_hex)
                key = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt, 100000)
                return key.hex() == key_hex
            
            elif method == "sha256":
                return hashlib.sha256(password.encode('utf-8')).hexdigest() == hashed_password
            
        except Exception:
            return False
    
    def generate_strong_password(self, length=12, include_symbols=True):
        """توليد كلمة مرور قوية"""
        # تحديد مجموعة الأحرف
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?" if include_symbols else ""
        
        # ضمان وجود حرف واحد على الأقل من كل نوع
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits)
        ]
        
        if include_symbols:
            password.append(secrets.choice(symbols))
        
        # ملء باقي الطول
        all_chars = lowercase + uppercase + digits + symbols
        for _ in range(length - len(password)):
            password.append(secrets.choice(all_chars))
        
        # خلط الأحرف
        secrets.SystemRandom().shuffle(password)
        
        return ''.join(password)
    
    def check_breached_password(self, password):
        """فحص كلمة المرور في قواعد البيانات المسربة"""
        # محاكاة فحص قاعدة بيانات HaveIBeenPwned
        password_lower = password.lower()
        
        if password_lower in self.breached_passwords:
            source = self.breached_passwords[password_lower]
            print(f"{Fore.RED}⚠️  كلمة المرور موجودة في: {source}{Style.RESET_ALL}")
            return True
        
        # محاكاة فحص SHA-1 hash (طريقة HaveIBeenPwned الحقيقية)
        sha1_hash = hashlib.sha1(password.encode('utf-8')).hexdigest().upper()
        prefix = sha1_hash[:5]
        
        print(f"{Fore.CYAN}فحص SHA-1 prefix: {prefix}...{Style.RESET_ALL}")
        print(f"{Fore.GREEN}لم يتم العثور على كلمة المرور في قواعد البيانات المسربة{Style.RESET_ALL}")
        
        return False
    
    def implement_rate_limiting(self, ip_address, max_attempts=None, block_duration=None):
        """تطبيق Rate Limiting"""
        if max_attempts is None:
            max_attempts = self.max_attempts
        if block_duration is None:
            block_duration = self.block_duration
        
        current_time = time.time()
        
        # فحص إذا كان IP محظور
        if ip_address in self.blocked_ips:
            block_time = self.blocked_ips[ip_address]
            if current_time - block_time < block_duration:
                remaining = block_duration - (current_time - block_time)
                return False, f"IP محظور لمدة {remaining:.0f} ثانية أخرى"
            else:
                # انتهاء فترة الحظر
                del self.blocked_ips[ip_address]
                if ip_address in self.login_attempts:
                    del self.login_attempts[ip_address]
        
        # تسجيل المحاولة
        if ip_address not in self.login_attempts:
            self.login_attempts[ip_address] = []
        
        # إزالة المحاولات القديمة (أكثر من ساعة)
        hour_ago = current_time - 3600
        self.login_attempts[ip_address] = [
            attempt for attempt in self.login_attempts[ip_address] 
            if attempt > hour_ago
        ]
        
        # إضافة المحاولة الحالية
        self.login_attempts[ip_address].append(current_time)
        
        # فحص تجاوز الحد الأقصى
        if len(self.login_attempts[ip_address]) > max_attempts:
            self.blocked_ips[ip_address] = current_time
            return False, f"تم حظر IP بعد {max_attempts} محاولات فاشلة"
        
        remaining_attempts = max_attempts - len(self.login_attempts[ip_address])
        return True, f"محاولة مسموحة. المحاولات المتبقية: {remaining_attempts}"
    
    def detect_suspicious_activity(self, login_data):
        """كشف النشاط المشبوه"""
        suspicious_indicators = []
        
        # فحص عدد المحاولات
        if login_data.get('attempts', 0) > 10:
            suspicious_indicators.append("عدد كبير من محاولات تسجيل الدخول")
        
        # فحص التوقيت
        current_hour = datetime.now().hour
        if current_hour < 6 or current_hour > 22:
            suspicious_indicators.append("محاولة دخول في وقت غير عادي")
        
        # فحص الموقع الجغرافي (محاكاة)
        if login_data.get('country') != 'SA':  # افتراض أن المستخدم من السعودية
            suspicious_indicators.append("محاولة دخول من دولة مختلفة")
        
        # فحص نوع المتصفح
        user_agent = login_data.get('user_agent', '')
        if 'bot' in user_agent.lower() or 'crawler' in user_agent.lower():
            suspicious_indicators.append("محاولة دخول من bot أو crawler")
        
        return suspicious_indicators
    
    def generate_2fa_code(self):
        """توليد رمز التحقق الثنائي"""
        # توليد رمز من 6 أرقام
        code = ''.join([str(secrets.randbelow(10)) for _ in range(6)])
        
        # محاكاة إرسال الرمز
        print(f"{Fore.CYAN}تم إرسال رمز التحقق: {code}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}الرمز صالح لمدة 5 دقائق{Style.RESET_ALL}")
        
        return code, time.time() + 300  # صالح لمدة 5 دقائق
    
    def verify_2fa_code(self, entered_code, generated_code, expiry_time):
        """التحقق من رمز التحقق الثنائي"""
        current_time = time.time()
        
        if current_time > expiry_time:
            return False, "انتهت صلاحية الرمز"
        
        if entered_code == generated_code:
            return True, "تم التحقق بنجاح"
        else:
            return False, "رمز خاطئ"
    
    def encrypt_sensitive_data(self, data):
        """تشفير البيانات الحساسة"""
        # توليد مفتاح تشفير
        key = Fernet.generate_key()
        cipher_suite = Fernet(key)
        
        # تشفير البيانات
        encrypted_data = cipher_suite.encrypt(data.encode('utf-8'))
        
        return {
            'key': key.decode('utf-8'),
            'encrypted_data': encrypted_data.decode('utf-8')
        }
    
    def decrypt_sensitive_data(self, key, encrypted_data):
        """فك تشفير البيانات الحساسة"""
        try:
            cipher_suite = Fernet(key.encode('utf-8'))
            decrypted_data = cipher_suite.decrypt(encrypted_data.encode('utf-8'))
            return decrypted_data.decode('utf-8')
        except Exception as e:
            return f"خطأ في فك التشفير: {str(e)}"
    
    def security_audit_report(self):
        """تقرير تدقيق الأمان"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_login_attempts': sum(len(attempts) for attempts in self.login_attempts.values()),
            'blocked_ips': len(self.blocked_ips),
            'active_sessions': len(self.login_attempts),
            'security_recommendations': [
                "استخدم كلمات مرور قوية ومعقدة",
                "فعل التحقق الثنائي (2FA)",
                "راقب محاولات تسجيل الدخول المشبوهة",
                "حدث كلمات المرور بانتظام",
                "استخدم مدير كلمات مرور موثوق",
                "فعل تشفير البيانات الحساسة"
            ]
        }
        
        return report
    
    def display_security_status(self):
        """عرض حالة الأمان"""
        print(f"\n{Fore.CYAN}{'='*50}")
        print(f"تقرير حالة الأمان")
        print(f"{'='*50}{Style.RESET_ALL}")
        
        report = self.security_audit_report()
        
        print(f"{Fore.YELLOW}الوقت:{Style.RESET_ALL} {report['timestamp']}")
        print(f"{Fore.YELLOW}إجمالي محاولات تسجيل الدخول:{Style.RESET_ALL} {report['total_login_attempts']}")
        print(f"{Fore.YELLOW}عناوين IP المحظورة:{Style.RESET_ALL} {report['blocked_ips']}")
        print(f"{Fore.YELLOW}الجلسات النشطة:{Style.RESET_ALL} {report['active_sessions']}")
        
        print(f"\n{Fore.CYAN}توصيات الأمان:{Style.RESET_ALL}")
        for i, rec in enumerate(report['security_recommendations'], 1):
            print(f"  {i}. {rec}")

if __name__ == "__main__":
    security = SecurityTools()
    
    # اختبار أدوات الأمان
    print(f"{Fore.MAGENTA}اختبار أدوات الأمان{Style.RESET_ALL}")
    
    # اختبار تشفير كلمة المرور
    password = "MySecurePassword123!"
    hashed = security.hash_password(password)
    print(f"\nكلمة المرور الأصلية: {password}")
    print(f"كلمة المرور المشفرة: {hashed}")
    
    # اختبار التحقق
    is_valid = security.verify_password(password, hashed)
    print(f"التحقق من كلمة المرور: {'صحيح' if is_valid else 'خاطئ'}")
    
    # اختبار توليد كلمة مرور قوية
    strong_password = security.generate_strong_password(16)
    print(f"\nكلمة مرور قوية مولدة: {strong_password}")
    
    # عرض تقرير الأمان
    security.display_security_status()
