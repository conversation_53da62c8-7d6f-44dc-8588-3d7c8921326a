#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشروع تحليل أمان كلمات المرور
Password Security Analysis Project

الملف الرئيسي للمشروع
"""

import os
import sys
from colorama import init, Fore, Style
from password_analyzer import PasswordAnalyzer
from attack_simulator import AttackSimulator
from security_tools import SecurityTools
from web_interface import WebInterface

# تهيئة الألوان
init(autoreset=True)

class PasswordSecurityProject:
    def __init__(self):
        self.analyzer = PasswordAnalyzer()
        self.simulator = AttackSimulator()
        self.security = SecurityTools()
        self.web = WebInterface()
        
    def print_banner(self):
        """طباعة شعار المشروع"""
        banner = f"""
{Fore.CYAN}
╔══════════════════════════════════════════════════════════════╗
║                    مشروع تحليل أمان كلمات المرور                    ║
║                Password Security Analysis Project            ║
╠══════════════════════════════════════════════════════════════╣
║  هذا مشروع تعليمي لفهم طرق الهجوم على كلمات المرور وكيفية الحماية منها  ║
║              Educational project for password security       ║
╚══════════════════════════════════════════════════════════════╝
{Style.RESET_ALL}
        """
        print(banner)
    
    def show_menu(self):
        """عرض القائمة الرئيسية"""
        menu = f"""
{Fore.YELLOW}الخيارات المتاحة:{Style.RESET_ALL}
{Fore.GREEN}1.{Style.RESET_ALL} تحليل قوة كلمة المرور
{Fore.GREEN}2.{Style.RESET_ALL} محاكي الهجمات التعليمية
{Fore.GREEN}3.{Style.RESET_ALL} أدوات الحماية والأمان
{Fore.GREEN}4.{Style.RESET_ALL} تشغيل واجهة الويب
{Fore.GREEN}5.{Style.RESET_ALL} عرض التقارير والإحصائيات
{Fore.GREEN}6.{Style.RESET_ALL} معلومات حول طرق الهجوم
{Fore.RED}0.{Style.RESET_ALL} خروج

{Fore.CYAN}اختر رقم الخيار:{Style.RESET_ALL} """
        return input(menu)
    
    def password_analysis_menu(self):
        """قائمة تحليل كلمات المرور"""
        password = input(f"{Fore.CYAN}أدخل كلمة المرور للتحليل: {Style.RESET_ALL}")
        if password:
            result = self.analyzer.analyze_password(password)
            self.analyzer.display_analysis(result)
        else:
            print(f"{Fore.RED}لم يتم إدخال كلمة مرور!{Style.RESET_ALL}")
    
    def attack_simulation_menu(self):
        """قائمة محاكي الهجمات"""
        print(f"\n{Fore.YELLOW}أنواع الهجمات المتاحة:{Style.RESET_ALL}")
        print(f"{Fore.GREEN}1.{Style.RESET_ALL} Brute Force Attack")
        print(f"{Fore.GREEN}2.{Style.RESET_ALL} Dictionary Attack")
        print(f"{Fore.GREEN}3.{Style.RESET_ALL} Credential Stuffing Simulation")
        print(f"{Fore.GREEN}4.{Style.RESET_ALL} Rate Limiting Test")
        
        choice = input(f"{Fore.CYAN}اختر نوع الهجوم: {Style.RESET_ALL}")
        target_password = input(f"{Fore.CYAN}أدخل كلمة المرور المستهدفة: {Style.RESET_ALL}")
        
        if choice == "1":
            self.simulator.brute_force_simulation(target_password)
        elif choice == "2":
            self.simulator.dictionary_attack_simulation(target_password)
        elif choice == "3":
            self.simulator.credential_stuffing_simulation(target_password)
        elif choice == "4":
            self.simulator.rate_limiting_test()
    
    def security_tools_menu(self):
        """قائمة أدوات الأمان"""
        print(f"\n{Fore.YELLOW}أدوات الأمان المتاحة:{Style.RESET_ALL}")
        print(f"{Fore.GREEN}1.{Style.RESET_ALL} تشفير كلمة المرور")
        print(f"{Fore.GREEN}2.{Style.RESET_ALL} التحقق من كلمة المرور المشفرة")
        print(f"{Fore.GREEN}3.{Style.RESET_ALL} توليد كلمة مرور قوية")
        print(f"{Fore.GREEN}4.{Style.RESET_ALL} فحص كلمة المرور في قواعد البيانات المسربة")
        
        choice = input(f"{Fore.CYAN}اختر الأداة: {Style.RESET_ALL}")
        
        if choice == "1":
            password = input(f"{Fore.CYAN}أدخل كلمة المرور للتشفير: {Style.RESET_ALL}")
            encrypted = self.security.hash_password(password)
            print(f"{Fore.GREEN}كلمة المرور المشفرة: {encrypted}{Style.RESET_ALL}")
        elif choice == "2":
            password = input(f"{Fore.CYAN}أدخل كلمة المرور: {Style.RESET_ALL}")
            hash_value = input(f"{Fore.CYAN}أدخل الهاش للمقارنة: {Style.RESET_ALL}")
            is_valid = self.security.verify_password(password, hash_value)
            print(f"{Fore.GREEN if is_valid else Fore.RED}النتيجة: {'صحيحة' if is_valid else 'خاطئة'}{Style.RESET_ALL}")
        elif choice == "3":
            length = int(input(f"{Fore.CYAN}طول كلمة المرور المطلوب (افتراضي 12): {Style.RESET_ALL}") or "12")
            strong_password = self.security.generate_strong_password(length)
            print(f"{Fore.GREEN}كلمة المرور القوية: {strong_password}{Style.RESET_ALL}")
        elif choice == "4":
            password = input(f"{Fore.CYAN}أدخل كلمة المرور للفحص: {Style.RESET_ALL}")
            is_breached = self.security.check_breached_password(password)
            print(f"{Fore.RED if is_breached else Fore.GREEN}النتيجة: {'موجودة في قواعد البيانات المسربة!' if is_breached else 'آمنة'}{Style.RESET_ALL}")
    
    def show_attack_info(self):
        """عرض معلومات حول طرق الهجوم"""
        info = f"""
{Fore.CYAN}معلومات حول طرق الهجوم على كلمات المرور:{Style.RESET_ALL}

{Fore.YELLOW}الطرق التقليدية:{Style.RESET_ALL}
{Fore.GREEN}✓{Style.RESET_ALL} Brute Force Attack: محاولة جميع التركيبات الممكنة
{Fore.GREEN}✓{Style.RESET_ALL} Dictionary Attack: استخدام قاموس كلمات شائعة
{Fore.GREEN}✓{Style.RESET_ALL} Credential Stuffing: استخدام كلمات مرور مسربة
{Fore.GREEN}✓{Style.RESET_ALL} Rate Limit Bypass: تجاوز حدود المحاولات
{Fore.GREEN}✓{Style.RESET_ALL} Phishing: صفحات مزيفة لسرقة البيانات
{Fore.GREEN}✓{Style.RESET_ALL} MITM & Session Hijacking: اعتراض الجلسات

{Fore.YELLOW}الطرق الحديثة:{Style.RESET_ALL}
{Fore.GREEN}✓{Style.RESET_ALL} Malicious Browser Extensions: إضافات المتصفح الخبيثة
{Fore.GREEN}✓{Style.RESET_ALL} Stealer Malware: برامج سرقة البيانات (RedLine, MetaStealer)
{Fore.GREEN}✓{Style.RESET_ALL} API Abuse: استغلال نقاط ضعف في APIs

{Fore.YELLOW}طرق الحماية:{Style.RESET_ALL}
{Fore.GREEN}✓{Style.RESET_ALL} استخدام كلمات مرور قوية ومعقدة
{Fore.GREEN}✓{Style.RESET_ALL} التحقق الثنائي (2FA)
{Fore.GREEN}✓{Style.RESET_ALL} Rate Limiting وحماية من الهجمات
{Fore.GREEN}✓{Style.RESET_ALL} تشفير كلمات المرور بطرق آمنة
{Fore.GREEN}✓{Style.RESET_ALL} مراقبة محاولات الدخول المشبوهة
        """
        print(info)
    
    def run(self):
        """تشغيل المشروع"""
        self.print_banner()
        
        while True:
            try:
                choice = self.show_menu()
                
                if choice == "0":
                    print(f"{Fore.GREEN}شكراً لاستخدام المشروع!{Style.RESET_ALL}")
                    break
                elif choice == "1":
                    self.password_analysis_menu()
                elif choice == "2":
                    self.attack_simulation_menu()
                elif choice == "3":
                    self.security_tools_menu()
                elif choice == "4":
                    print(f"{Fore.CYAN}تشغيل واجهة الويب...{Style.RESET_ALL}")
                    self.web.run()
                elif choice == "5":
                    print(f"{Fore.CYAN}عرض التقارير...{Style.RESET_ALL}")
                    # سيتم تطوير هذا لاحقاً
                elif choice == "6":
                    self.show_attack_info()
                else:
                    print(f"{Fore.RED}خيار غير صحيح!{Style.RESET_ALL}")
                    
                input(f"\n{Fore.CYAN}اضغط Enter للمتابعة...{Style.RESET_ALL}")
                os.system('cls' if os.name == 'nt' else 'clear')
                self.print_banner()
                
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}تم إيقاف المشروع بواسطة المستخدم{Style.RESET_ALL}")
                break
            except Exception as e:
                print(f"{Fore.RED}خطأ: {str(e)}{Style.RESET_ALL}")

if __name__ == "__main__":
    project = PasswordSecurityProject()
    project.run()
