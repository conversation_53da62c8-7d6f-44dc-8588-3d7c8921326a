# مشروع تحليل أمان كلمات المرور
## Password Security Analysis Project

### الهدف من المشروع
هذا مشروع تعليمي لفهم طرق الهجوم على كلمات المرور وكيفية الحماية منها.

### المكونات الرئيسية:

#### 1. تحليل قوة كلمات المرور
- فحص تعقيد كلمة المرور
- تقدير الوقت المطلوب لكسرها
- اقتراحات للتحسين

#### 2. محاكي الهجمات التعليمية
- **Brute Force Attack**: محاولة جميع التركيبات الممكنة
- **Dictionary Attack**: استخدام قاموس كلمات شائعة
- **Credential Stuffing**: اختبار كلمات مرور مسربة
- **Rate Limiting**: محاكاة الحماية من الهجمات

#### 3. أدوات الحماية
- كشف محاولات الهجوم
- تطبيق Rate Limiting
- تشفير كلمات المرور
- التحقق الثنائي

#### 4. التقارير والإحصائيات
- تحليل أنماط الهجمات
- إحصائيات الأمان
- توصيات الحماية

### كيفية الاستخدام:
```bash
pip install -r requirements.txt
python main.py
```

### تحذير مهم:
هذا المشروع للأغراض التعليمية فقط. لا تستخدمه لأغراض غير قانونية.

### الملفات:
- `main.py`: الملف الرئيسي
- `password_analyzer.py`: تحليل قوة كلمات المرور
- `attack_simulator.py`: محاكي الهجمات التعليمية
- `security_tools.py`: أدوات الحماية
- `web_interface.py`: واجهة الويب
- `data/`: مجلد البيانات والقواميس
- `reports/`: مجلد التقارير
